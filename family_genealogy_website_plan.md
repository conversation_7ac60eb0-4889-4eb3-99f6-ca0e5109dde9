# 家族宗谱网站规划

本文件将项目整体划分为 **三段**，每段包含关键目标、主要功能以及实现要点，便于逐步推进开发。

---

## 第一阶段：需求分析 & 基础搭建
### 目标
- 明确网站定位与核心用户（家族成员、研究者）。
- 搭建最小可运行的原型站点，提供基本的族谱浏览功能。

### 核心功能
1. **首页**  
   - 项目简介、使用说明。  
   - 简洁导航栏（首页 / 族谱树 / 成员列表 / 联系我们）。

2. **族谱树展示**  
   - 使用交互式树形图（如 D3.js）显示家族关系。  
   - 支持点击节点展开子代信息。

3. **成员详情页**  
   - 基本信息：姓名、出生/逝世日期、照片、简介。  
   - 可编辑的简短传记（后期加入权限控制）。

### 技术选型
| 类别 | 方案 |
|------|------|
| 前端框架 | **React** + Vite（快速热更新） |
| UI 库 | **Tailwind CSS**（原子化样式，易定制） |
| 树形图 | **d3-hierarchy** 或 **react-d3-tree** |
| 后端 | **Node.js (Express)** 简单 REST API |
| 数据存储 | **SQLite**（轻量、文件即数据库），后期可迁移至 PostgreSQL |
| 部署 | Vercel / Netlify（前端） + Railway（后端） |

### 实现要点
- 项目结构采用 monorepo（`/frontend`, `/backend`）。  
- 使用 **JSON** 或 **YAML** 预置少量族谱数据，先实现静态渲染。  
- 配置 CORS 与基本错误处理。

---

## 第二阶段：交互增强 & 内容管理
### 目标
- 为非技术用户提供友好的内容编辑与上传方式。  
- 增强族谱树的交互性和可视化效果。

### 核心功能
1. **后台管理系统**（Admin Dashboard）  
   - 登录/权限（管理员 vs 普通成员）。  
   - CRUD：添加、编辑、删除成员信息及关系。  

2. **文件上传**  
   - 支持照片、文档（PDF、扫描件）上传并关联到成员。  
   - 使用 **Multer** 中间件处理文件，存储于本地或云对象存储。

3. **高级族谱树交互**  
   - 拖拽重新排布节点位置。  
   - 搜索功能：按姓名、年代快速定位。

4. **多语言/本地化**（可选）  
   - 支持中文与英文双语界面，使用 i18next 实现。

### 技术实现
- 后端加入 **JWT** 鉴权，实现安全的 API。  
- 前端使用 **React Router** 管理页面路由。  
- 数据模型采用 **ORM（Prisma）**，简化迁移与查询。  
- 文件存储可先本地 `uploads/` 目录，后期切换至 S3。

### 实现要点
- 为每个成员生成唯一 ID，便于树形结构的关联。  
- 前端使用 **React Query** 管理异步数据缓存，提高响应速度。  
- 编写单元测试（Jest + React Testing Library）确保关键功能可靠。

---

## 第三阶段：发布、运营与扩展
### 目标
- 将网站正式上线，提供稳定的访问服务。  
- 引入社区贡献机制，让家族成员自行维护族谱。

### 核心功能
1. **用户注册 & 社区贡献**  
   - 成员可申请成为编辑者，提交信息审核后发布。  

2. **历史数据导入/导出**  
   - 支持 GEDCOM（常用的族谱文件格式）导入已有家谱数据。  
   - 导出为 PDF、CSV 供离线保存。

3. **搜索与过滤**  
   - 按年代、地区、职业等多维度筛选成员列表。  

4. **性能优化 & SEO**  
   - 静态化关键页面（Next.js 或 Astro），提升加载速度。  
   - 添加结构化数据（Schema.org FamilyTree）帮助搜索引擎抓取。

5. **监控与备份**  
   - 使用 **PM2** 监控后端进程，定时数据库备份至云存储。  

### 部署方案
- 前端使用 **Netlify** 自动构建并提供 CDN 加速。  
- 后端部署在 **Railway** 或 **Render**，配置自动扩容。  
- 使用 **GitHub Actions** 实现 CI/CD：代码推送 → 自动测试 → 部署。

### 运营建议
- 建立家族专属的 Discord/Telegram 群组，收集需求与反馈。  
- 定期组织线上“族谱日”，邀请成员更新信息。  
- 在网站加入纪念日倒计时、生日提醒等互动功能，提高活跃度。

---

# 结语
以上三段规划从 **最小可行产品（MVP）** 到 **完整运营平台**，层层递进，兼顾技术实现与用户体验。你可以先在本地完成第一阶段的原型验证，确认需求后再逐步推进后续功能。祝开发顺利，期待看到你的家族宗谱网站上线！
